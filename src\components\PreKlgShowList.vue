<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { getPreKlgQuesListApi } from '@/apis/path/klg';
import type { params2GetPreKlgList } from '@/apis/path/klg';
import { MAX_PAGESIZE, QuestionTypeDict } from '@/utils/constant';
const props = defineProps({
  klgCode: String,
  questionList: Array // 接收问题列表
});
const emits = defineEmits(['select']);
const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);

const curKlgCode = ref('');
const listData = ref<any[]>([]); // 表格数据

// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getPreKlgQuesList(newPage);
};
// 获取前驱知识列表
const getPreKlgQuesList = (page: number) => {
  const params: params2GetPreKlgList = {
    current: page ? page : currentPage.value,
    limit: pageSize,
    klgCode: curKlgCode.value
  };
  getPreKlgQuesListApi(params).then((res) => {
    if (res.success) {
      listData.value = res.data.records;
      total.value = res.data.total;
    }
  });
};
// 处理选择 - 直接打开ShowAnswerDrawer
const handleSelect = (data: any) => {
  // 保持原有的select事件触发，以防其他地方有依赖
  emits('select', data);

  // 参考KlgInfo.vue的实现，直接触发显示答案抽屉的自定义事件
  const showAnswerEvent = new CustomEvent('showAnswerFromFloating', {
    detail: { question: data }
  });
  window.dispatchEvent(showAnswerEvent);
};
watch(
  () => props,
  () => {
    if (props.klgCode && props.klgCode !== '' && props.klgCode !== null) {
      curKlgCode.value = props.klgCode;
      getPreKlgQuesList(currentPage.value);
    }
  },
  { immediate: true }
);
</script>
<template>
  <div class="wrapper">
    <div class="header">
      <span class="header-title">前驱知识</span>
    </div>
    <div class="main-container">
      <div class="list-total">
        <span>共{{ total }}个</span>
      </div>
      <div class="pre-klg-list" v-if="total === 0" style="padding-bottom: 10px">暂无数据</div>
      <div class="pre-klg-list" v-else>
        <div
          class="pre-klg-item"
          v-for="item in listData"
          :key="item.id"
          @click="handleSelect(item)"
        >
          <span class="klg-left">
            <span
              class="ellipsis-text-inline"
              v-html="item.keyword"
              style="width: 100%; display: inline-flex; align-items: center"
            ></span>
          </span>
          <span class="klg-right">
            <span class="klg-type">{{ item.questionType }}</span>
            <img src="@/assets/image/klg/u1705.svg" />
          </span>
        </div>
      </div>
      <div class="footer">
        <el-pagination
          :v-model:current-page="currentPage"
          :page-size="pageSize"
          :pager-count="7"
          layout="prev, pager, next"
          :total="total"
          @change="handleChangePage"
        />
      </div>
    </div>
  </div>
</template>
<style scoped>
.image-fix {
  :deep(img) {
    max-width: 60px;
    height: auto;
  }
}

.wrapper {
  width: 390px;
  background-color: white;
  padding: 10px;
  display: flex;
  flex-direction: column !important;
  font-size: 14px;
  .header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .header-title {
      font-size: 14px;
      font-weight: 600;
    }
    .question-count {
      font-size: 12px;
      color: var(--color-grey);
      margin-left: 8px;
    }
  }
  .main-container {
    display: flex;
    flex-direction: column;
    .list-total {
      display: flex;
      justify-content: flex-end;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    .pre-klg-list {
      background-color: var(--color-light);
      padding: 10px 10px 0 10px;
      .pre-klg-item {
        display: flex;
        justify-content: space-between;
        align-items: center; /* 垂直居中 */
        background-color: white;
        padding: 5px 10px;
        margin-bottom: 10px;
        border: 1px solid var(--color-boxborder);
        border-radius: 3px;
        cursor: pointer;
        height: 55px;
        min-height: 55px; /* 确保最小高度 */
        :deep(p) {
          margin: 0;
        }
        &:hover {
          background-color: var(--color-light);
        }
        .klg-left {
          flex: 1;
          margin-right: 5px;
          display: flex;
          align-items: center;
          height: 100%;
          max-width: 80%; /* 限制最大宽度，确保有足够空间显示右侧内容 */
          overflow: hidden; /* 确保溢出内容被隐藏 */
        }
        .klg-right {
          display: flex;
          align-items: center;
          .klg-type {
            margin-right: 5px;
            min-width: 42px;
          }
        }
      }
    }
    .footer {
      display: flex;
      justify-content: center;
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
